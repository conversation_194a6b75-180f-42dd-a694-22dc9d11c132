// ===== FINE EXPRESS CARGO - GALLERY FUNCTIONALITY =====

class GalleryManager {
    constructor() {
        this.currentFilter = 'all';
        this.currentLightboxIndex = 0;
        this.galleryItems = [];
        this.filteredItems = [];
        this.itemsPerPage = 12;
        this.currentPage = 1;
        this.isLightboxOpen = false;
        
        this.init();
    }
    
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeGallery());
        } else {
            this.initializeGallery();
        }
    }
    
    initializeGallery() {
        this.setupGalleryItems();
        this.setupFilterButtons();
        this.setupLightbox();
        this.setupLoadMore();
        this.setupKeyboardNavigation();
        
        // Initial filter
        this.filterItems('all');
    }
    
    // ===== GALLERY ITEMS SETUP =====
    setupGalleryItems() {
        const galleryGrid = document.getElementById('gallery-grid');
        if (!galleryGrid) return;
        
        this.galleryItems = Array.from(galleryGrid.querySelectorAll('.gallery-item'));
        
        // Add click event listeners to gallery items
        this.galleryItems.forEach((item, index) => {
            const viewBtn = item.querySelector('.gallery-view-btn');
            const image = item.querySelector('.gallery-image img');
            
            if (viewBtn) {
                viewBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.openLightbox(index);
                });
            }
            
            if (image) {
                image.addEventListener('click', () => {
                    this.openLightbox(index);
                });
            }
            
            // Add hover effects
            item.addEventListener('mouseenter', () => {
                this.addHoverEffect(item);
            });
            
            item.addEventListener('mouseleave', () => {
                this.removeHoverEffect(item);
            });
        });
    }
    
    addHoverEffect(item) {
        const overlay = item.querySelector('.gallery-overlay');
        if (overlay) {
            overlay.style.opacity = '1';
        }
    }
    
    removeHoverEffect(item) {
        // Overlay opacity is handled by CSS hover states
    }
    
    // ===== FILTER FUNCTIONALITY =====
    setupFilterButtons() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        
        filterButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const filter = btn.getAttribute('data-filter');
                this.filterItems(filter);
                this.updateActiveFilter(btn);
            });
        });
    }
    
    updateActiveFilter(activeBtn) {
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => btn.classList.remove('active'));
        activeBtn.classList.add('active');
    }
    
    filterItems(filter) {
        this.currentFilter = filter;
        this.currentPage = 1;
        
        // Filter items based on category
        if (filter === 'all') {
            this.filteredItems = [...this.galleryItems];
        } else {
            this.filteredItems = this.galleryItems.filter(item => {
                return item.getAttribute('data-category') === filter;
            });
        }
        
        // Hide all items first
        this.galleryItems.forEach(item => {
            item.classList.add('hidden');
            item.style.display = 'none';
        });
        
        // Show filtered items with animation
        this.showFilteredItems();
        
        // Update load more button
        this.updateLoadMoreButton();
    }
    
    showFilteredItems() {
        const itemsToShow = this.filteredItems.slice(0, this.itemsPerPage * this.currentPage);
        
        itemsToShow.forEach((item, index) => {
            setTimeout(() => {
                item.classList.remove('hidden');
                item.style.display = 'block';
                item.classList.add('animate-fade-in-up');
                
                // Remove animation class after animation completes
                setTimeout(() => {
                    item.classList.remove('animate-fade-in-up');
                }, 600);
            }, index * 50); // Stagger the animations
        });
    }
    
    // ===== LOAD MORE FUNCTIONALITY =====
    setupLoadMore() {
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreItems();
            });
        }
    }
    
    loadMoreItems() {
        this.currentPage++;
        this.showFilteredItems();
        this.updateLoadMoreButton();
    }
    
    updateLoadMoreButton() {
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (!loadMoreBtn) return;
        
        const totalItems = this.filteredItems.length;
        const shownItems = this.itemsPerPage * this.currentPage;
        
        if (shownItems >= totalItems) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'inline-flex';
        }
    }
    
    // ===== LIGHTBOX FUNCTIONALITY =====
    setupLightbox() {
        const lightboxModal = document.getElementById('lightbox-modal');
        const lightboxClose = document.getElementById('lightbox-close');
        const lightboxPrev = document.getElementById('lightbox-prev');
        const lightboxNext = document.getElementById('lightbox-next');
        
        if (!lightboxModal) return;
        
        // Close lightbox
        if (lightboxClose) {
            lightboxClose.addEventListener('click', () => {
                this.closeLightbox();
            });
        }
        
        // Navigation buttons
        if (lightboxPrev) {
            lightboxPrev.addEventListener('click', () => {
                this.previousImage();
            });
        }
        
        if (lightboxNext) {
            lightboxNext.addEventListener('click', () => {
                this.nextImage();
            });
        }
        
        // Close on backdrop click
        lightboxModal.addEventListener('click', (e) => {
            if (e.target === lightboxModal) {
                this.closeLightbox();
            }
        });
        
        // Touch/swipe support for mobile
        this.setupTouchNavigation(lightboxModal);
    }
    
    setupTouchNavigation(lightboxModal) {
        let startX = 0;
        let startY = 0;
        
        lightboxModal.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        lightboxModal.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // Only handle horizontal swipes
            if (Math.abs(diffX) > Math.abs(diffY)) {
                if (Math.abs(diffX) > 50) { // Minimum swipe distance
                    if (diffX > 0) {
                        this.nextImage();
                    } else {
                        this.previousImage();
                    }
                }
            }
            
            startX = 0;
            startY = 0;
        });
    }
    
    openLightbox(index) {
        const visibleItems = this.getVisibleItems();
        const item = visibleItems[index];
        
        if (!item) return;
        
        this.currentLightboxIndex = index;
        this.isLightboxOpen = true;
        
        const lightboxModal = document.getElementById('lightbox-modal');
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxTitle = document.getElementById('lightbox-title');
        const lightboxDescription = document.getElementById('lightbox-description');
        const lightboxCurrent = document.getElementById('lightbox-current');
        const lightboxTotal = document.getElementById('lightbox-total');
        
        if (!lightboxModal || !lightboxImage) return;
        
        // Get image and info
        const img = item.querySelector('.gallery-image img');
        const title = item.querySelector('.gallery-info h3');
        const description = item.querySelector('.gallery-info p');
        
        // Update lightbox content
        lightboxImage.src = img.src;
        lightboxImage.alt = img.alt;
        
        if (lightboxTitle && title) {
            lightboxTitle.textContent = title.textContent;
        }
        
        if (lightboxDescription && description) {
            lightboxDescription.textContent = description.textContent;
        }
        
        // Update counter
        if (lightboxCurrent && lightboxTotal) {
            lightboxCurrent.textContent = index + 1;
            lightboxTotal.textContent = visibleItems.length;
        }
        
        // Show lightbox
        lightboxModal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Update navigation buttons
        this.updateLightboxNavigation();
    }
    
    closeLightbox() {
        const lightboxModal = document.getElementById('lightbox-modal');
        if (lightboxModal) {
            lightboxModal.classList.remove('show');
            document.body.style.overflow = '';
            this.isLightboxOpen = false;
        }
    }
    
    previousImage() {
        const visibleItems = this.getVisibleItems();
        if (this.currentLightboxIndex > 0) {
            this.currentLightboxIndex--;
        } else {
            this.currentLightboxIndex = visibleItems.length - 1; // Loop to last
        }
        this.updateLightboxContent();
    }
    
    nextImage() {
        const visibleItems = this.getVisibleItems();
        if (this.currentLightboxIndex < visibleItems.length - 1) {
            this.currentLightboxIndex++;
        } else {
            this.currentLightboxIndex = 0; // Loop to first
        }
        this.updateLightboxContent();
    }
    
    updateLightboxContent() {
        const visibleItems = this.getVisibleItems();
        const item = visibleItems[this.currentLightboxIndex];
        
        if (!item) return;
        
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxTitle = document.getElementById('lightbox-title');
        const lightboxDescription = document.getElementById('lightbox-description');
        const lightboxCurrent = document.getElementById('lightbox-current');
        
        // Get image and info
        const img = item.querySelector('.gallery-image img');
        const title = item.querySelector('.gallery-info h3');
        const description = item.querySelector('.gallery-info p');
        
        // Update content with fade effect
        if (lightboxImage) {
            lightboxImage.style.opacity = '0';
            setTimeout(() => {
                lightboxImage.src = img.src;
                lightboxImage.alt = img.alt;
                lightboxImage.style.opacity = '1';
            }, 150);
        }
        
        if (lightboxTitle && title) {
            lightboxTitle.textContent = title.textContent;
        }
        
        if (lightboxDescription && description) {
            lightboxDescription.textContent = description.textContent;
        }
        
        if (lightboxCurrent) {
            lightboxCurrent.textContent = this.currentLightboxIndex + 1;
        }
        
        this.updateLightboxNavigation();
    }
    
    updateLightboxNavigation() {
        const lightboxPrev = document.getElementById('lightbox-prev');
        const lightboxNext = document.getElementById('lightbox-next');
        const visibleItems = this.getVisibleItems();
        
        if (lightboxPrev && lightboxNext) {
            // Always show navigation buttons for looping
            lightboxPrev.style.display = 'block';
            lightboxNext.style.display = 'block';
            
            // Add visual feedback for first/last items
            if (this.currentLightboxIndex === 0) {
                lightboxPrev.style.opacity = '0.5';
            } else {
                lightboxPrev.style.opacity = '1';
            }
            
            if (this.currentLightboxIndex === visibleItems.length - 1) {
                lightboxNext.style.opacity = '0.5';
            } else {
                lightboxNext.style.opacity = '1';
            }
        }
    }
    
    getVisibleItems() {
        return this.galleryItems.filter(item => !item.classList.contains('hidden'));
    }
    
    // ===== KEYBOARD NAVIGATION =====
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            if (!this.isLightboxOpen) return;
            
            switch (e.key) {
                case 'Escape':
                    this.closeLightbox();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousImage();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.nextImage();
                    break;
            }
        });
    }
    
    // ===== PUBLIC API =====
    filterByCategory(category) {
        this.filterItems(category);
        
        // Update active filter button
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-filter') === category) {
                btn.classList.add('active');
            }
        });
    }
    
    openImageInLightbox(index) {
        this.openLightbox(index);
    }
    
    getCurrentFilter() {
        return this.currentFilter;
    }
    
    getTotalItems() {
        return this.galleryItems.length;
    }
    
    getFilteredItemsCount() {
        return this.filteredItems.length;
    }
}

// ===== INITIALIZE GALLERY =====
// Only initialize if we're on the gallery page
if (window.location.pathname.includes('gallery.html') || document.getElementById('gallery-grid')) {
    const galleryManager = new GalleryManager();
    
    // Make gallery manager available globally
    window.galleryManager = galleryManager;
}
