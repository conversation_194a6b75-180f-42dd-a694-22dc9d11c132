<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Fine Express Cargo Admin Dashboard - Manage messages, subscribers, and service updates">
    <meta name="author" content="Fine Express Cargo">
    
    <title>Admin Dashboard - Fine Express Cargo</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/styles.css">
    
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .admin-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            border-radius: var(--border-radius-lg);
            margin-bottom: 2rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .profile-section {
            position: relative;
        }

        .profile-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all var(--transition-fast);
        }

        .profile-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            box-shadow: var(--shadow-lg);
            min-width: 120px;
            display: none;
            z-index: 1000;
        }

        .profile-dropdown.show {
            display: block;
        }

        .profile-dropdown a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--text-primary);
            text-decoration: none;
            transition: background-color var(--transition-fast);
        }

        .profile-dropdown a:hover {
            background-color: var(--bg-secondary);
        }
        
        .admin-nav {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .admin-nav button {
            padding: 0.75rem 1.5rem;
            border: none;
            background: var(--bg-secondary);
            color: var(--text-primary);
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .admin-nav button.active {
            background: var(--primary-color);
            color: white;
        }
        
        .admin-section {
            display: none;
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
        }
        
        .admin-section.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: var(--border-radius-md);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .data-table th {
            background: var(--bg-secondary);
            font-weight: bold;
        }
        
        .action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            margin-right: 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-secondary {
            background: var(--secondary-color);
            color: white;
        }
        
        .btn-danger {
            background: var(--error-color);
            color: white;
        }
        
        .message-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .status-new {
            background: var(--warning-color);
            color: white;
        }
        
        .status-read {
            background: var(--success-color);
            color: white;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            min-width: 200px;
        }

        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            background: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .campaigns-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }

        .tab-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-fast);
        }

        .tab-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .template-card {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: 1.5rem;
            background: var(--bg-secondary);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .template-type {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
        }

        .template-preview {
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: var(--border-radius-sm);
            margin: 1rem 0;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .template-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .draft-item {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: var(--bg-secondary);
        }

        .draft-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: var(--border-radius-lg);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div class="header-content">
                <div>
                    <h1>Fine Express Cargo - Admin Dashboard</h1>
                    <p>Manage messages, subscribers, and service updates</p>
                </div>
                <div class="header-actions">
                    <button class="action-btn btn-secondary" id="back-to-website-btn" style="margin-right: 1rem;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" style="margin-right: 0.5rem;">
                            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                        </svg>
                        Back to Website
                    </button>
                    <div class="profile-section">
                        <button class="profile-btn" id="profile-btn">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            <span>Admin</span>
                        </button>
                        <div class="profile-dropdown" id="profile-dropdown">
                            <a href="#" id="logout-btn">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <nav class="admin-nav">
            <button class="nav-btn active" data-section="dashboard">Dashboard</button>
            <button class="nav-btn" data-section="messages">Messages</button>
            <button class="nav-btn" data-section="subscribers">Subscribers</button>
            <button class="nav-btn" data-section="email-campaigns">Email Campaigns</button>
        </nav>
        
        <!-- Dashboard Section -->
        <section id="dashboard" class="admin-section active">
            <h2>Dashboard Overview</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-messages">12</div>
                    <div class="stat-label">Total Messages</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="new-messages">3</div>
                    <div class="stat-label">New Messages</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-subscribers">45</div>
                    <div class="stat-label">Total Subscribers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="this-week-signups">8</div>
                    <div class="stat-label">This Week Signups</div>
                </div>
            </div>
            
            <h3>Recent Activity</h3>
            <div class="recent-activity">
                <p>• New message from John Doe about import services</p>
                <p>• 3 new subscribers joined the service network</p>
                <p>• Weekly collection schedule updated</p>
                <p>• Email campaign sent to 42 subscribers</p>
            </div>
        </section>
        
        <!-- Messages Section -->
        <section id="messages" class="admin-section">
            <div class="section-header">
                <h2>Contact Messages</h2>
                <div class="section-actions">
                    <input type="text" id="message-search" placeholder="Search messages..." class="search-input">
                    <select id="message-filter" class="filter-select">
                        <option value="all">All Messages</option>
                        <option value="new">New</option>
                        <option value="read">Read</option>
                    </select>
                    <button class="action-btn btn-secondary" id="export-messages">Export Messages</button>
                </div>
            </div>

            <table class="data-table" id="messages-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Subject</th>
                        <th>Message</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="messages-tbody">
                    <tr data-id="1" data-status="new">
                        <td>2024-01-15</td>
                        <td>John Doe</td>
                        <td><EMAIL></td>
                        <td>Import Inquiry</td>
                        <td class="message-preview" title="I need to import goods from Dubai to Tanzania. Can you provide pricing and schedule information?">I need to import goods from Dubai to Tanzania...</td>
                        <td><span class="status-badge status-new">New</span></td>
                        <td>
                            <button class="action-btn btn-primary reply-btn" data-id="1">Reply</button>
                            <button class="action-btn btn-secondary mark-read-btn" data-id="1">Mark Read</button>
                            <button class="action-btn btn-danger delete-btn" data-id="1">Delete</button>
                        </td>
                    </tr>
                    <tr data-id="2" data-status="read">
                        <td>2024-01-14</td>
                        <td>Sarah Ahmed</td>
                        <td><EMAIL></td>
                        <td>Delivery Schedule</td>
                        <td class="message-preview" title="When is the next delivery to Dar es Salaam? I have urgent cargo waiting.">When is the next delivery to Dar es Salaam...</td>
                        <td><span class="status-badge status-read">Read</span></td>
                        <td>
                            <button class="action-btn btn-primary reply-btn" data-id="2">Reply</button>
                            <button class="action-btn btn-secondary mark-unread-btn" data-id="2">Mark Unread</button>
                            <button class="action-btn btn-danger delete-btn" data-id="2">Delete</button>
                        </td>
                    </tr>
                    <tr data-id="3" data-status="new">
                        <td>2024-01-13</td>
                        <td>Ahmed Hassan</td>
                        <td><EMAIL></td>
                        <td>Collection Service</td>
                        <td class="message-preview" title="I need to schedule a collection from Dubai on Friday. What documents do I need?">I need to schedule a collection from Dubai...</td>
                        <td><span class="status-badge status-new">New</span></td>
                        <td>
                            <button class="action-btn btn-primary reply-btn" data-id="3">Reply</button>
                            <button class="action-btn btn-secondary mark-read-btn" data-id="3">Mark Read</button>
                            <button class="action-btn btn-danger delete-btn" data-id="3">Delete</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>
        
        <!-- Subscribers Section -->
        <section id="subscribers" class="admin-section">
            <div class="section-header">
                <h2>Service Network Subscribers</h2>
                <div class="section-actions">
                    <input type="text" id="subscriber-search" placeholder="Search subscribers..." class="search-input">
                    <button class="action-btn btn-primary" id="export-subscribers">Export List</button>
                    <button class="action-btn btn-secondary" id="bulk-email-btn">Send Bulk Email</button>
                    <button class="action-btn btn-success" id="add-subscriber-btn">Add Subscriber</button>
                </div>
            </div>

            <table class="data-table" id="subscribers-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="select-all-subscribers"></th>
                        <th>Date Joined</th>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Location</th>
                        <th>Goods Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="subscribers-tbody">
                    <tr data-id="1">
                        <td><input type="checkbox" class="subscriber-checkbox" data-id="1"></td>
                        <td>2024-01-15</td>
                        <td>Ahmed Hassan</td>
                        <td>+255 123 456 789</td>
                        <td><EMAIL></td>
                        <td>Dar es Salaam</td>
                        <td>Electronics</td>
                        <td>
                            <button class="action-btn btn-primary whatsapp-btn" data-phone="+255123456789">WhatsApp</button>
                            <button class="action-btn btn-secondary email-btn" data-email="<EMAIL>">Email</button>
                            <button class="action-btn btn-warning edit-subscriber-btn" data-id="1">Edit</button>
                            <button class="action-btn btn-danger delete-subscriber-btn" data-id="1">Delete</button>
                        </td>
                    </tr>
                    <tr data-id="2">
                        <td><input type="checkbox" class="subscriber-checkbox" data-id="2"></td>
                        <td>2024-01-14</td>
                        <td>Fatima Ali</td>
                        <td>+255 987 654 321</td>
                        <td><EMAIL></td>
                        <td>Arusha</td>
                        <td>Textiles</td>
                        <td>
                            <button class="action-btn btn-primary whatsapp-btn" data-phone="+255987654321">WhatsApp</button>
                            <button class="action-btn btn-secondary email-btn" data-email="<EMAIL>">Email</button>
                            <button class="action-btn btn-warning edit-subscriber-btn" data-id="2">Edit</button>
                            <button class="action-btn btn-danger delete-subscriber-btn" data-id="2">Delete</button>
                        </td>
                    </tr>
                    <tr data-id="3">
                        <td><input type="checkbox" class="subscriber-checkbox" data-id="3"></td>
                        <td>2024-01-13</td>
                        <td>Mohamed Salim</td>
                        <td>+255 555 123 456</td>
                        <td><EMAIL></td>
                        <td>Mwanza</td>
                        <td>Machinery</td>
                        <td>
                            <button class="action-btn btn-primary whatsapp-btn" data-phone="+255555123456">WhatsApp</button>
                            <button class="action-btn btn-secondary email-btn" data-email="<EMAIL>">Email</button>
                            <button class="action-btn btn-warning edit-subscriber-btn" data-id="3">Edit</button>
                            <button class="action-btn btn-danger delete-subscriber-btn" data-id="3">Delete</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>
        
        <!-- Email Campaigns Section -->
        <section id="email-campaigns" class="admin-section">
            <div class="section-header">
                <h2>Email Campaigns</h2>
                <div class="section-actions">
                    <button class="action-btn btn-primary" id="create-campaign-btn">Create New Campaign</button>
                    <button class="action-btn btn-secondary" id="campaign-analytics-btn">View Analytics</button>
                </div>
            </div>

            <div class="campaigns-tabs">
                <button class="tab-btn active" data-tab="templates">Templates</button>
                <button class="tab-btn" data-tab="sent">Sent Campaigns</button>
                <button class="tab-btn" data-tab="drafts">Drafts</button>
            </div>

            <div id="templates-tab" class="tab-content active">
                <h3>Campaign Templates</h3>
                <div class="templates-grid">
                    <div class="template-card">
                        <div class="template-header">
                            <h4>Weekly Schedule Update</h4>
                            <span class="template-type">Newsletter</span>
                        </div>
                        <p>Notify subscribers about collection and delivery schedules for the upcoming week</p>
                        <div class="template-preview">
                            <strong>Subject:</strong> Weekly Collection & Delivery Schedule - Fine Express Cargo<br>
                            <strong>Content:</strong> Dear valued customer, here's your weekly schedule...
                        </div>
                        <div class="template-actions">
                            <button class="action-btn btn-primary use-template-btn" data-template="schedule">Use Template</button>
                            <button class="action-btn btn-secondary preview-template-btn" data-template="schedule">Preview</button>
                            <button class="action-btn btn-warning edit-template-btn" data-template="schedule">Edit</button>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <h4>Service Announcement</h4>
                            <span class="template-type">Announcement</span>
                        </div>
                        <p>Announce new services, route updates, or important company news</p>
                        <div class="template-preview">
                            <strong>Subject:</strong> Important Service Update - Fine Express Cargo<br>
                            <strong>Content:</strong> We're excited to announce...
                        </div>
                        <div class="template-actions">
                            <button class="action-btn btn-primary use-template-btn" data-template="announcement">Use Template</button>
                            <button class="action-btn btn-secondary preview-template-btn" data-template="announcement">Preview</button>
                            <button class="action-btn btn-warning edit-template-btn" data-template="announcement">Edit</button>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <h4>Welcome Email</h4>
                            <span class="template-type">Automated</span>
                        </div>
                        <p>Automated welcome email sent to new subscribers</p>
                        <div class="template-preview">
                            <strong>Subject:</strong> Welcome to Fine Express Cargo Service Network<br>
                            <strong>Content:</strong> Thank you for joining our service network...
                        </div>
                        <div class="template-actions">
                            <button class="action-btn btn-secondary preview-template-btn" data-template="welcome">Preview</button>
                            <button class="action-btn btn-warning edit-template-btn" data-template="welcome">Edit</button>
                            <button class="action-btn btn-success test-template-btn" data-template="welcome">Send Test</button>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-header">
                            <h4>Delivery Notification</h4>
                            <span class="template-type">Transactional</span>
                        </div>
                        <p>Notify customers about upcoming deliveries and tracking information</p>
                        <div class="template-preview">
                            <strong>Subject:</strong> Your Cargo is Ready for Delivery - Fine Express Cargo<br>
                            <strong>Content:</strong> Your shipment will be delivered on Tuesday...
                        </div>
                        <div class="template-actions">
                            <button class="action-btn btn-primary use-template-btn" data-template="delivery">Use Template</button>
                            <button class="action-btn btn-secondary preview-template-btn" data-template="delivery">Preview</button>
                            <button class="action-btn btn-warning edit-template-btn" data-template="delivery">Edit</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="sent-tab" class="tab-content">
                <h3>Sent Campaigns</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date Sent</th>
                            <th>Campaign Name</th>
                            <th>Recipients</th>
                            <th>Open Rate</th>
                            <th>Click Rate</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024-01-15</td>
                            <td>Weekly Schedule Update</td>
                            <td>45</td>
                            <td>78%</td>
                            <td>23%</td>
                            <td>
                                <button class="action-btn btn-secondary" onclick="previewEmail('welcome-email')">Preview</button>
                                <button class="action-btn btn-warning" onclick="editCampaign('welcome-email')">Edit</button>
                                <button class="action-btn btn-success" onclick="sendTestEmail('welcome-email')">Send Test</button>
                                <button class="action-btn btn-info" onclick="viewAnalytics('welcome-email')">View Analytics</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2024-01-10</td>
                            <td>New Route Announcement</td>
                            <td>42</td>
                            <td>82%</td>
                            <td>31%</td>
                            <td>
                                <button class="action-btn btn-secondary" onclick="previewEmail('service-update')">Preview</button>
                                <button class="action-btn btn-warning" onclick="editCampaign('service-update')">Edit</button>
                                <button class="action-btn btn-success" onclick="sendTestEmail('service-update')">Send Test</button>
                                <button class="action-btn btn-info" onclick="viewAnalytics('service-update')">View Analytics</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="drafts-tab" class="tab-content">
                <h3>Draft Campaigns</h3>
                <div class="drafts-list">
                    <div class="draft-item">
                        <h4>Holiday Schedule Changes</h4>
                        <p>Last edited: 2024-01-14</p>
                        <div class="draft-actions">
                            <button class="action-btn btn-primary">Continue Editing</button>
                            <button class="action-btn btn-secondary">Preview</button>
                            <button class="action-btn btn-danger">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    
    <!-- Modals -->
    <div id="reply-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Reply to Message</h3>
                <button class="close-modal" onclick="closeModal('reply-modal')">&times;</button>
            </div>
            <form id="reply-form">
                <div class="form-group">
                    <label>To:</label>
                    <input type="email" id="reply-to" readonly>
                </div>
                <div class="form-group">
                    <label>Subject:</label>
                    <input type="text" id="reply-subject">
                </div>
                <div class="form-group">
                    <label>Message:</label>
                    <textarea id="reply-message" placeholder="Type your reply here..."></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="action-btn btn-primary">Send Reply</button>
                    <button type="button" class="action-btn btn-secondary" onclick="closeModal('reply-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <div id="bulk-email-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Send Bulk Email</h3>
                <button class="close-modal" onclick="closeModal('bulk-email-modal')">&times;</button>
            </div>
            <form id="bulk-email-form">
                <div class="form-group">
                    <label>Recipients:</label>
                    <select id="bulk-recipients">
                        <option value="all">All Subscribers</option>
                        <option value="selected">Selected Subscribers</option>
                        <option value="location">By Location</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Subject:</label>
                    <input type="text" id="bulk-subject" placeholder="Email subject">
                </div>
                <div class="form-group">
                    <label>Message:</label>
                    <textarea id="bulk-message" placeholder="Email content..."></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="action-btn btn-primary">Send Email</button>
                    <button type="button" class="action-btn btn-secondary" onclick="closeModal('bulk-email-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <div id="campaign-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create Email Campaign</h3>
                <button class="close-modal" onclick="closeModal('campaign-modal')">&times;</button>
            </div>
            <form id="campaign-form">
                <div class="form-group">
                    <label>Campaign Name:</label>
                    <input type="text" id="campaign-name" placeholder="Campaign name">
                </div>
                <div class="form-group">
                    <label>Subject:</label>
                    <input type="text" id="campaign-subject" placeholder="Email subject">
                </div>
                <div class="form-group">
                    <label>Recipients:</label>
                    <select id="campaign-recipients">
                        <option value="all">All Subscribers</option>
                        <option value="location">By Location</option>
                        <option value="goods">By Goods Type</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Content:</label>
                    <textarea id="campaign-content" placeholder="Email content..."></textarea>
                </div>
                <div class="form-group">
                    <button type="submit" class="action-btn btn-primary">Create Campaign</button>
                    <button type="button" class="action-btn btn-secondary" onclick="closeModal('campaign-modal')">Save as Draft</button>
                    <button type="button" class="action-btn btn-warning" onclick="closeModal('campaign-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Subscriber Modal -->
    <div id="add-subscriber-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Subscriber</h3>
                <button class="close-modal" onclick="closeModal('add-subscriber-modal')">&times;</button>
            </div>
            <form id="add-subscriber-form">
                <div class="form-group">
                    <label>Full Name *</label>
                    <input type="text" id="new-subscriber-name" placeholder="Enter full name" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Phone Number *</label>
                    <input type="tel" id="new-subscriber-phone" placeholder="+255 XXX XXX XXX" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Email Address *</label>
                    <input type="email" id="new-subscriber-email" placeholder="<EMAIL>" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Location/City *</label>
                    <input type="text" id="new-subscriber-location" placeholder="Enter location" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Type of Goods</label>
                    <select id="new-subscriber-goods">
                        <option value="">Select goods type</option>
                        <option value="Electronics">Electronics</option>
                        <option value="Textiles">Textiles</option>
                        <option value="Machinery">Machinery</option>
                        <option value="Food Products">Food Products</option>
                        <option value="Automotive Parts">Automotive Parts</option>
                        <option value="Medical Supplies">Medical Supplies</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="action-btn btn-primary">Add Subscriber</button>
                    <button type="button" class="action-btn btn-secondary" onclick="closeModal('add-subscriber-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Subscriber Modal -->
    <div id="edit-subscriber-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Subscriber</h3>
                <button class="close-modal" onclick="closeModal('edit-subscriber-modal')">&times;</button>
            </div>
            <form id="edit-subscriber-form">
                <input type="hidden" id="edit-subscriber-id">
                <div class="form-group">
                    <label>Full Name *</label>
                    <input type="text" id="edit-subscriber-name" placeholder="Enter full name" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Phone Number *</label>
                    <input type="tel" id="edit-subscriber-phone" placeholder="+255 XXX XXX XXX" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Email Address *</label>
                    <input type="email" id="edit-subscriber-email" placeholder="<EMAIL>" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Location/City *</label>
                    <input type="text" id="edit-subscriber-location" placeholder="Enter location" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label>Type of Goods</label>
                    <select id="edit-subscriber-goods">
                        <option value="">Select goods type</option>
                        <option value="Electronics">Electronics</option>
                        <option value="Textiles">Textiles</option>
                        <option value="Machinery">Machinery</option>
                        <option value="Food Products">Food Products</option>
                        <option value="Automotive Parts">Automotive Parts</option>
                        <option value="Medical Supplies">Medical Supplies</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="action-btn btn-primary">Update Subscriber</button>
                    <button type="button" class="action-btn btn-secondary" onclick="closeModal('edit-subscriber-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Comprehensive admin dashboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            setupEventListeners();
            loadDashboardData();
        });

        function initializeDashboard() {
            const navButtons = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.admin-section');

            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetSection = this.dataset.section;

                    // Update active nav button
                    navButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Show target section
                    sections.forEach(section => section.classList.remove('active'));
                    document.getElementById(targetSection).classList.add('active');
                });
            });

            // Profile dropdown
            const profileBtn = document.getElementById('profile-btn');
            const profileDropdown = document.getElementById('profile-dropdown');

            profileBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('show');
            });

            document.addEventListener('click', function() {
                profileDropdown.classList.remove('show');
            });

            // Logout functionality
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to logout?')) {
                    logout();
                }
            });

            // Back to Website functionality
            document.getElementById('back-to-website-btn').addEventListener('click', function() {
                window.location.href = 'index.html';
            });
        }

        function setupEventListeners() {
            // Messages functionality
            setupMessagesListeners();

            // Subscribers functionality
            setupSubscribersListeners();

            // Email campaigns functionality
            setupCampaignsListeners();

            // Search and filter functionality
            setupSearchAndFilter();
        }

        function setupMessagesListeners() {
            // Reply buttons
            document.querySelectorAll('.reply-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const messageId = this.dataset.id;
                    openReplyModal(messageId);
                });
            });

            // Mark read/unread buttons
            document.querySelectorAll('.mark-read-btn, .mark-unread-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const messageId = this.dataset.id;
                    const isRead = this.classList.contains('mark-read-btn');
                    toggleMessageStatus(messageId, isRead);
                });
            });

            // Delete buttons
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const messageId = this.dataset.id;
                    deleteMessage(messageId);
                });
            });

            // Export messages
            document.getElementById('export-messages').addEventListener('click', exportMessages);
        }

        function setupSubscribersListeners() {
            // Export subscribers
            document.getElementById('export-subscribers').addEventListener('click', exportSubscribers);

            // Add subscriber
            document.getElementById('add-subscriber-btn').addEventListener('click', function() {
                openModal('add-subscriber-modal');
            });

            // Bulk email
            document.getElementById('bulk-email-btn').addEventListener('click', function() {
                openModal('bulk-email-modal');
            });

            // WhatsApp buttons
            document.querySelectorAll('.whatsapp-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const phone = this.dataset.phone;
                    openWhatsApp(phone);
                });
            });

            // Email buttons
            document.querySelectorAll('.email-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const email = this.dataset.email;
                    openEmailClient(email);
                });
            });

            // Edit subscriber buttons
            document.querySelectorAll('.edit-subscriber-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const subscriberId = this.dataset.id;
                    openEditSubscriberModal(subscriberId);
                });
            });

            // Delete subscriber buttons
            document.querySelectorAll('.delete-subscriber-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const subscriberId = this.dataset.id;
                    deleteSubscriber(subscriberId);
                });
            });

            // Select all checkbox
            document.getElementById('select-all-subscribers').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.subscriber-checkbox');
                checkboxes.forEach(cb => cb.checked = this.checked);
            });

            // Add subscriber form
            document.getElementById('add-subscriber-form').addEventListener('submit', handleAddSubscriber);

            // Edit subscriber form
            document.getElementById('edit-subscriber-form').addEventListener('submit', handleEditSubscriber);
        }

        function setupCampaignsListeners() {
            // Create campaign
            document.getElementById('create-campaign-btn').addEventListener('click', function() {
                openModal('campaign-modal');
            });

            // Tab switching
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const tabName = this.dataset.tab;
                    switchTab(tabName);
                });
            });

            // Template buttons
            document.querySelectorAll('.use-template-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const template = this.dataset.template;
                    useTemplate(template);
                });
            });

            document.querySelectorAll('.preview-template-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const template = this.dataset.template;
                    previewTemplate(template);
                });
            });
        }

        function setupSearchAndFilter() {
            // Message search
            document.getElementById('message-search').addEventListener('input', function() {
                filterMessages(this.value);
            });

            // Message filter
            document.getElementById('message-filter').addEventListener('change', function() {
                filterMessagesByStatus(this.value);
            });

            // Subscriber search
            document.getElementById('subscriber-search').addEventListener('input', function() {
                filterSubscribers(this.value);
            });
        }

        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // Message functions
        function openReplyModal(messageId) {
            const row = document.querySelector(`tr[data-id="${messageId}"]`);
            const email = row.cells[2].textContent;
            const subject = row.cells[3].textContent;

            document.getElementById('reply-to').value = email;
            document.getElementById('reply-subject').value = `Re: ${subject}`;

            openModal('reply-modal');
        }

        function toggleMessageStatus(messageId, markAsRead) {
            const row = document.querySelector(`tr[data-id="${messageId}"]`);
            const statusBadge = row.querySelector('.status-badge');
            const button = row.querySelector(markAsRead ? '.mark-read-btn' : '.mark-unread-btn');

            if (markAsRead) {
                statusBadge.textContent = 'Read';
                statusBadge.className = 'status-badge status-read';
                button.textContent = 'Mark Unread';
                button.className = 'action-btn btn-secondary mark-unread-btn';
                row.dataset.status = 'read';
            } else {
                statusBadge.textContent = 'New';
                statusBadge.className = 'status-badge status-new';
                button.textContent = 'Mark Read';
                button.className = 'action-btn btn-secondary mark-read-btn';
                row.dataset.status = 'new';
            }

            updateDashboardStats();
            showNotification(`Message marked as ${markAsRead ? 'read' : 'unread'}`, 'success');
        }

        function deleteMessage(messageId) {
            if (confirm('Are you sure you want to delete this message?')) {
                const row = document.querySelector(`tr[data-id="${messageId}"]`);
                row.remove();
                updateDashboardStats();
                showNotification('Message deleted successfully', 'success');
            }
        }

        function exportMessages() {
            const messages = [];
            const rows = document.querySelectorAll('#messages-tbody tr');

            rows.forEach(row => {
                messages.push({
                    date: row.cells[0].textContent,
                    name: row.cells[1].textContent,
                    email: row.cells[2].textContent,
                    subject: row.cells[3].textContent,
                    message: row.cells[4].title || row.cells[4].textContent,
                    status: row.dataset.status
                });
            });

            downloadCSV(messages, 'messages.csv');
            showNotification('Messages exported successfully', 'success');
        }

        // Subscriber functions
        function exportSubscribers() {
            const subscribers = [];
            const rows = document.querySelectorAll('#subscribers-tbody tr');

            rows.forEach(row => {
                subscribers.push({
                    dateJoined: row.cells[1].textContent,
                    name: row.cells[2].textContent,
                    phone: row.cells[3].textContent,
                    email: row.cells[4].textContent,
                    location: row.cells[5].textContent,
                    goodsType: row.cells[6].textContent
                });
            });

            downloadCSV(subscribers, 'subscribers.csv');
            showNotification('Subscribers exported successfully', 'success');
        }

        function openWhatsApp(phone) {
            const message = encodeURIComponent('Hello! This is a message from Fine Express Cargo admin.');
            const url = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${message}`;
            window.open(url, '_blank');
        }

        function openEmailClient(email) {
            const subject = encodeURIComponent('Fine Express Cargo - Service Update');
            const body = encodeURIComponent('Dear valued customer,\n\nThank you for being part of our service network.\n\nBest regards,\nFine Express Cargo Team');
            window.location.href = `mailto:${email}?subject=${subject}&body=${body}`;
        }

        // Campaign functions
        function switchTab(tabName) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}-tab`).classList.add('active');
        }

        // Send Test Email Function
        function sendTestEmail(campaignId) {
            console.log('Opening test email modal for campaign:', campaignId);

            // Get campaign data for test email
            const campaignData = {
                'welcome-email': {
                    name: 'Welcome Email Campaign',
                    subject: 'Welcome to Fine Express Cargo Service Network'
                },
                'service-update': {
                    name: 'Service Update Newsletter',
                    subject: 'Service Update - Fine Express Cargo'
                },
                'schedule-notification': {
                    name: 'Schedule Notification',
                    subject: 'Weekly Collection Schedule Reminder'
                }
            };

            const campaign = campaignData[campaignId] || {
                name: 'Test Campaign',
                subject: 'Test Email from Fine Express Cargo'
            };

            // Create and show test email modal
            const modalHTML = `
                <div id="testEmailModal" class="modal-overlay" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                ">
                    <div class="modal-content" style="
                        background: white;
                        border-radius: 8px;
                        padding: 20px;
                        max-width: 500px;
                        width: 90%;
                        max-height: 80vh;
                        overflow-y: auto;
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h2 style="color: #1e3a8a; margin: 0;">Send Test Email</h2>
                            <button onclick="closeTestEmailModal()" style="
                                background: none;
                                border: none;
                                font-size: 24px;
                                cursor: pointer;
                                color: #666;
                            ">&times;</button>
                        </div>

                        <form onsubmit="submitTestEmail(event, '${campaignId}')">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">Campaign:</label>
                                <input type="text" value="${campaign.name}" readonly style="
                                    width: 100%;
                                    padding: 8px;
                                    border: 1px solid #ddd;
                                    border-radius: 4px;
                                    background: #f5f5f5;
                                ">
                            </div>

                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">Subject:</label>
                                <input type="text" value="${campaign.subject}" readonly style="
                                    width: 100%;
                                    padding: 8px;
                                    border: 1px solid #ddd;
                                    border-radius: 4px;
                                    background: #f5f5f5;
                                ">
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label for="testEmailAddresses" style="display: block; margin-bottom: 5px; font-weight: bold; color: #333;">Test Email Addresses:</label>
                                <textarea id="testEmailAddresses" placeholder="Enter email addresses separated by commas&#10;Example: <EMAIL>, <EMAIL>" required style="
                                    width: 100%;
                                    height: 80px;
                                    padding: 8px;
                                    border: 1px solid #ddd;
                                    border-radius: 4px;
                                    resize: vertical;
                                "></textarea>
                                <small style="color: #666; font-size: 12px;">Separate multiple email addresses with commas</small>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeTestEmailModal()" style="
                                    background: #6b7280;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                ">Cancel</button>
                                <button type="submit" style="
                                    background: #1e3a8a;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                ">Send Test Email</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeTestEmailModal() {
            const modal = document.getElementById('testEmailModal');
            if (modal) {
                modal.remove();
            }
        }

        function submitTestEmail(event, campaignId) {
            event.preventDefault();

            const emailAddresses = document.getElementById('testEmailAddresses').value;

            if (!emailAddresses.trim()) {
                alert('Please enter at least one email address');
                return;
            }

            // Validate email addresses
            const emails = emailAddresses.split(',').map(email => email.trim());
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const invalidEmails = emails.filter(email => !emailRegex.test(email));

            if (invalidEmails.length > 0) {
                alert(`Invalid email addresses: ${invalidEmails.join(', ')}`);
                return;
            }

            // Here you would send the test email to your backend
            console.log('Sending test email for campaign:', campaignId);
            console.log('To addresses:', emails);

            // Show success message
            alert(`Test email sent successfully to ${emails.length} address(es)!`);

            // Close modal
            closeTestEmailModal();
        }

        function useTemplate(templateName) {
            const templates = {
                schedule: {
                    name: 'Weekly Schedule Update',
                    subject: 'Weekly Collection & Delivery Schedule - Fine Express Cargo',
                    content: `Dear Valued Customer,

Here's your weekly collection and delivery schedule:

COLLECTION DAYS: Wednesday, Thursday, Friday
DELIVERY DAY: Tuesday
TRANSIT TIME: 4-5 days from Dubai to Tanzania

For any questions, please contact us:
Tanzania: +255 657 769 101
Dubai: +971 543 862 737

Best regards,
Fine Express Cargo Team`
                },
                announcement: {
                    name: 'Service Announcement',
                    subject: 'Important Service Update - Fine Express Cargo',
                    content: `Dear Valued Customer,

We're excited to announce important updates to our services.

[Your announcement content here]

Thank you for choosing Fine Express Cargo.

Best regards,
Fine Express Cargo Team`
                },
                delivery: {
                    name: 'Delivery Notification',
                    subject: 'Your Cargo is Ready for Delivery - Fine Express Cargo',
                    content: `Dear Customer,

Your shipment is ready for delivery on Tuesday.

Tracking Information:
- Collection Date: [Date]
- Expected Delivery: Tuesday
- Status: In Transit

For any questions, please contact us.

Best regards,
Fine Express Cargo Team`
                }
            };

            const template = templates[templateName];
            if (template) {
                document.getElementById('campaign-name').value = template.name;
                document.getElementById('campaign-subject').value = template.subject;
                document.getElementById('campaign-content').value = template.content;
                openModal('campaign-modal');
            }
        }

        function previewTemplate(templateName) {
            // This would open a preview window/modal
            alert(`Preview for ${templateName} template would open here`);
        }

        // Utility functions
        function filterMessages(searchTerm) {
            const rows = document.querySelectorAll('#messages-tbody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
            });
        }

        function filterMessagesByStatus(status) {
            const rows = document.querySelectorAll('#messages-tbody tr');
            rows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function filterSubscribers(searchTerm) {
            const rows = document.querySelectorAll('#subscribers-tbody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
            });
        }

        function downloadCSV(data, filename) {
            if (data.length === 0) return;

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                border-radius: 0.5rem;
                z-index: 1001;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function updateDashboardStats() {
            const totalMessages = document.querySelectorAll('#messages-tbody tr').length;
            const newMessages = document.querySelectorAll('#messages-tbody tr[data-status="new"]').length;

            document.getElementById('total-messages').textContent = totalMessages;
            document.getElementById('new-messages').textContent = newMessages;
        }

        function loadDashboardData() {
            // Simulate loading real-time data
            updateDashboardStats();

            // Simulate real-time updates
            setInterval(function() {
                if (Math.random() > 0.98) { // 2% chance every interval
                    const newMessages = document.getElementById('new-messages');
                    const currentCount = parseInt(newMessages.textContent);
                    newMessages.textContent = currentCount + 1;

                    showNotification('New message received!', 'info');
                }
            }, 5000);
        }

        // Form submissions
        document.getElementById('reply-form').addEventListener('submit', function(e) {
            e.preventDefault();
            showNotification('Reply sent successfully!', 'success');
            closeModal('reply-modal');
            this.reset();
        });

        document.getElementById('bulk-email-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const recipients = document.getElementById('bulk-recipients').value;
            showNotification(`Bulk email sent to ${recipients} recipients!`, 'success');
            closeModal('bulk-email-modal');
            this.reset();
        });

        document.getElementById('campaign-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const campaignName = document.getElementById('campaign-name').value;
            showNotification(`Campaign "${campaignName}" created successfully!`, 'success');
            closeModal('campaign-modal');
            this.reset();
        });

        // Enhanced Subscriber Management Functions
        function handleAddSubscriber(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('new-subscriber-name').value.trim(),
                phone: document.getElementById('new-subscriber-phone').value.trim(),
                email: document.getElementById('new-subscriber-email').value.trim(),
                location: document.getElementById('new-subscriber-location').value.trim(),
                goods: document.getElementById('new-subscriber-goods').value
            };

            // Validate form
            if (!validateSubscriberForm(formData, 'new')) {
                return;
            }

            // Generate new ID
            const newId = Date.now();
            const currentDate = new Date().toISOString().split('T')[0];

            // Add to table
            addSubscriberToTable(newId, currentDate, formData);

            // Save to localStorage (simulating database)
            saveSubscriberToStorage(newId, currentDate, formData);

            // Close modal and reset form
            closeModal('add-subscriber-modal');
            document.getElementById('add-subscriber-form').reset();

            // Update stats
            updateDashboardStats();

            showNotification('Subscriber added successfully!', 'success');
        }

        function validateSubscriberForm(data, type) {
            let isValid = true;
            const prefix = type === 'new' ? 'new-subscriber' : 'edit-subscriber';

            // Clear previous errors
            document.querySelectorAll(`#${prefix}-form .error-message`).forEach(el => {
                el.textContent = '';
                el.previousElementSibling.classList.remove('error');
            });

            // Validate name
            if (!data.name) {
                showFieldError(`${prefix}-name`, 'Name is required');
                isValid = false;
            }

            // Validate phone
            if (!data.phone) {
                showFieldError(`${prefix}-phone`, 'Phone number is required');
                isValid = false;
            } else if (!/^\+?[\d\s-()]+$/.test(data.phone)) {
                showFieldError(`${prefix}-phone`, 'Please enter a valid phone number');
                isValid = false;
            }

            // Validate email
            if (!data.email) {
                showFieldError(`${prefix}-email`, 'Email is required');
                isValid = false;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                showFieldError(`${prefix}-email`, 'Please enter a valid email address');
                isValid = false;
            }

            // Validate location
            if (!data.location) {
                showFieldError(`${prefix}-location`, 'Location is required');
                isValid = false;
            }

            return isValid;
        }

        function showFieldError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorEl = field.nextElementSibling;
            field.classList.add('error');
            errorEl.textContent = message;
        }

        function addSubscriberToTable(id, date, data) {
            const tbody = document.getElementById('subscribers-tbody');
            const row = document.createElement('tr');
            row.dataset.id = id;

            row.innerHTML = `
                <td><input type="checkbox" class="subscriber-checkbox" data-id="${id}"></td>
                <td>${date}</td>
                <td>${data.name}</td>
                <td>${data.phone}</td>
                <td>${data.email}</td>
                <td>${data.location}</td>
                <td>${data.goods || 'Not specified'}</td>
                <td>
                    <button class="action-btn btn-primary whatsapp-btn" data-phone="${data.phone}">WhatsApp</button>
                    <button class="action-btn btn-secondary email-btn" data-email="${data.email}">Email</button>
                    <button class="action-btn btn-warning edit-subscriber-btn" data-id="${id}">Edit</button>
                    <button class="action-btn btn-danger delete-subscriber-btn" data-id="${id}">Delete</button>
                </td>
            `;

            tbody.appendChild(row);

            // Re-attach event listeners for new buttons
            setupSubscriberRowListeners(row);
        }

        function setupSubscriberRowListeners(row) {
            // WhatsApp button
            row.querySelector('.whatsapp-btn').addEventListener('click', function() {
                openWhatsApp(this.dataset.phone);
            });

            // Email button
            row.querySelector('.email-btn').addEventListener('click', function() {
                openEmailClient(this.dataset.email);
            });

            // Edit button
            row.querySelector('.edit-subscriber-btn').addEventListener('click', function() {
                openEditSubscriberModal(this.dataset.id);
            });

            // Delete button
            row.querySelector('.delete-subscriber-btn').addEventListener('click', function() {
                deleteSubscriber(this.dataset.id);
            });
        }

        function openEditSubscriberModal(subscriberId) {
            const row = document.querySelector(`#subscribers-tbody tr[data-id="${subscriberId}"]`);
            if (row) {
                document.getElementById('edit-subscriber-id').value = subscriberId;
                document.getElementById('edit-subscriber-name').value = row.cells[2].textContent;
                document.getElementById('edit-subscriber-phone').value = row.cells[3].textContent;
                document.getElementById('edit-subscriber-email').value = row.cells[4].textContent;
                document.getElementById('edit-subscriber-location').value = row.cells[5].textContent;
                document.getElementById('edit-subscriber-goods').value = row.cells[6].textContent === 'Not specified' ? '' : row.cells[6].textContent;

                openModal('edit-subscriber-modal');
            }
        }

        function deleteSubscriber(subscriberId) {
            if (confirm('Are you sure you want to delete this subscriber?')) {
                const row = document.querySelector(`#subscribers-tbody tr[data-id="${subscriberId}"]`);
                if (row) {
                    row.remove();
                    removeSubscriberFromStorage(subscriberId);
                    updateDashboardStats();
                    showNotification('Subscriber deleted successfully', 'success');
                }
            }
        }

        // LocalStorage functions (simulating database)
        function saveSubscriberToStorage(id, date, data) {
            const subscribers = JSON.parse(localStorage.getItem('fec_subscribers') || '[]');
            subscribers.push({ id, date, ...data });
            localStorage.setItem('fec_subscribers', JSON.stringify(subscribers));
        }

        function removeSubscriberFromStorage(id) {
            const subscribers = JSON.parse(localStorage.getItem('fec_subscribers') || '[]');
            const filtered = subscribers.filter(s => s.id != id);
            localStorage.setItem('fec_subscribers', JSON.stringify(filtered));
        }

        // ===== DASHBOARD BUTTON FUNCTIONS =====

        // View Analytics Function
        function viewAnalytics(campaignId) {
          console.log('Opening analytics for campaign:', campaignId);

          // Sample analytics data (replace with real data)
          const analyticsData = {
            'welcome-email': {
              name: 'Welcome Email Campaign',
              sent: 150,
              delivered: 145,
              opened: 89,
              clicked: 23,
              bounced: 5,
              openRate: '61.4%',
              clickRate: '25.8%',
              bounceRate: '3.4%'
            },
            'service-update': {
              name: 'Service Update Newsletter',
              sent: 200,
              delivered: 195,
              opened: 112,
              clicked: 34,
              bounced: 5,
              openRate: '57.4%',
              clickRate: '30.4%',
              bounceRate: '2.6%'
            },
            'schedule-notification': {
              name: 'Schedule Notification',
              sent: 180,
              delivered: 175,
              opened: 98,
              clicked: 28,
              bounced: 5,
              openRate: '56.0%',
              clickRate: '28.6%',
              bounceRate: '2.8%'
            }
          };

          const campaign = analyticsData[campaignId] || {
            name: 'Campaign Analytics',
            sent: 0,
            delivered: 0,
            opened: 0,
            clicked: 0,
            bounced: 0,
            openRate: '0%',
            clickRate: '0%',
            bounceRate: '0%'
          };

          // Create and show analytics modal
          const modalHTML = `
            <div id="analyticsModal" class="modal-overlay" style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0,0,0,0.5);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 1000;
            ">
              <div class="modal-content" style="
                background: white;
                border-radius: 8px;
                padding: 30px;
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
              ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                  <h2 style="color: #1e3a8a; margin: 0;">Campaign Analytics</h2>
                  <button onclick="closeAnalyticsModal()" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                  ">&times;</button>
                </div>

                <h3 style="color: #333; margin-bottom: 20px;">${campaign.name}</h3>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                  <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #1e3a8a;">${campaign.sent}</div>
                    <div style="color: #666; font-size: 14px;">Emails Sent</div>
                  </div>
                  <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #28a745;">${campaign.delivered}</div>
                    <div style="color: #666; font-size: 14px;">Delivered</div>
                  </div>
                  <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #fb923c;">${campaign.opened}</div>
                    <div style="color: #666; font-size: 14px;">Opened</div>
                  </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                  <div style="text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #1e3a8a;">${campaign.openRate}</div>
                    <div style="color: #666; font-size: 12px;">Open Rate</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #fb923c;">${campaign.clickRate}</div>
                    <div style="color: #666; font-size: 12px;">Click Rate</div>
                  </div>
                  <div style="text-align: center;">
                    <div style="font-size: 18px; font-weight: bold; color: #dc3545;">${campaign.bounceRate}</div>
                    <div style="color: #666; font-size: 12px;">Bounce Rate</div>
                  </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                  <button onclick="closeAnalyticsModal()" style="
                    background: #1e3a8a;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                  ">Close</button>
                </div>
              </div>
            </div>
          `;

          document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeAnalyticsModal() {
          const modal = document.getElementById('analyticsModal');
          if (modal) {
            modal.remove();
          }
        }

        // Preview Email Function
        function previewEmail(templateId) {
          console.log('Opening email preview for template:', templateId);

          // Email templates data
          const emailTemplates = {
            'welcome-email': {
              subject: 'Welcome to Fine Express Cargo Service Network',
              content: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <div style="background: #1e3a8a; color: white; padding: 20px; text-align: center;">
                    <h1 style="margin: 0;">Welcome to Fine Express Cargo</h1>
                  </div>
                  <div style="padding: 20px;">
                    <p>Dear Valued Customer,</p>
                    <p>Thank you for joining our service network! We're excited to help you with your import and export needs between Tanzania, Dubai, and China.</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                      <h3 style="color: #1e3a8a; margin-top: 0;">Our Weekly Schedule:</h3>
                      <ul>
                        <li><strong>Collection Days:</strong> Wednesday, Thursday, Friday</li>
                        <li><strong>Delivery Day:</strong> Tuesday</li>
                        <li><strong>Transit Time:</strong> 4-5 days</li>
                      </ul>
                    </div>
                    <p>Best regards,<br>Fine Express Cargo Team</p>
                  </div>
                </div>
              `
            },
            'service-update': {
              subject: 'Service Update - Fine Express Cargo',
              content: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <div style="background: #fb923c; color: white; padding: 20px; text-align: center;">
                    <h1 style="margin: 0;">Service Update</h1>
                  </div>
                  <div style="padding: 20px;">
                    <p>Dear Customer,</p>
                    <p>We have important updates regarding our collection and delivery services.</p>
                    <p>Please check our website for the latest schedule information.</p>
                    <p>For any questions, contact us:</p>
                    <ul>
                      <li>Tanzania: +255 657769101</li>
                      <li>Dubai: +971 543862737</li>
                    </ul>
                    <p>Thank you for your continued trust in Fine Express Cargo.</p>
                  </div>
                </div>
              `
            },
            'schedule-notification': {
              subject: 'Weekly Collection Schedule Reminder',
              content: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <div style="background: #1e3a8a; color: white; padding: 20px; text-align: center;">
                    <h1 style="margin: 0;">Collection Schedule Reminder</h1>
                  </div>
                  <div style="padding: 20px;">
                    <p>Dear Customer,</p>
                    <p>This is a reminder about this week's collection schedule:</p>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                      <h3 style="color: #1e3a8a; margin-top: 0;">This Week's Schedule:</h3>
                      <ul>
                        <li><strong>Wednesday:</strong> Collection Day</li>
                        <li><strong>Thursday:</strong> Collection Day</li>
                        <li><strong>Friday:</strong> Collection Day</li>
                        <li><strong>Tuesday:</strong> Delivery Day</li>
                      </ul>
                    </div>
                    <p>Please have your packages ready for collection.</p>
                    <p>Best regards,<br>Fine Express Cargo Team</p>
                  </div>
                </div>
              `
            }
          };

          const template = emailTemplates[templateId] || {
            subject: 'Email Template',
            content: '<p>Template content not found.</p>'
          };

          // Create and show preview modal
          const modalHTML = `
            <div id="previewModal" class="modal-overlay" style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0,0,0,0.5);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 1000;
            ">
              <div class="modal-content" style="
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 700px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
              ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                  <h2 style="color: #1e3a8a; margin: 0;">Email Preview</h2>
                  <button onclick="closePreviewModal()" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                  ">&times;</button>
                </div>

                <div style="border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
                  <div style="background: #f8f9fa; padding: 10px; border-bottom: 1px solid #ddd;">
                    <strong>Subject:</strong> ${template.subject}
                  </div>
                  <div style="padding: 0;">
                    ${template.content}
                  </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                  <button onclick="closePreviewModal()" style="
                    background: #1e3a8a;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                  ">Close Preview</button>
                </div>
              </div>
            </div>
          `;

          document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closePreviewModal() {
          const modal = document.getElementById('previewModal');
          if (modal) {
            modal.remove();
          }
        }

        // Edit Campaign Function
        function editCampaign(campaignId) {
          console.log('Opening edit form for campaign:', campaignId);

          // Get campaign data (replace with real data)
          const campaignData = {
            'welcome-email': {
              name: 'Welcome Email Campaign',
              subject: 'Welcome to Fine Express Cargo Service Network',
              content: 'Welcome email content here...'
            },
            'service-update': {
              name: 'Service Update Newsletter',
              subject: 'Service Update - Fine Express Cargo',
              content: 'Service update content here...'
            },
            'schedule-notification': {
              name: 'Schedule Notification',
              subject: 'Weekly Collection Schedule Reminder',
              content: 'Schedule notification content here...'
            }
          };

          const campaign = campaignData[campaignId] || {
            name: 'New Campaign',
            subject: '',
            content: ''
          };

          // Create and show edit modal
          const modalHTML = `
            <div id="editModal" class="modal-overlay" style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0,0,0,0.5);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 1000;
            ">
              <div class="modal-content" style="
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 700px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
              ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                  <h2 style="color: #1e3a8a; margin: 0;">Edit Campaign</h2>
                  <button onclick="closeEditModal()" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                  ">&times;</button>
                </div>

                <form id="editCampaignForm" onsubmit="saveCampaign(event, '${campaignId}')">
                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Campaign Name:</label>
                    <input type="text" id="campaignName" value="${campaign.name}" style="
                      width: 100%;
                      padding: 8px;
                      border: 1px solid #ddd;
                      border-radius: 4px;
                      font-size: 14px;
                    " required>
                  </div>

                  <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Subject:</label>
                    <input type="text" id="campaignSubject" value="${campaign.subject}" style="
                      width: 100%;
                      padding: 8px;
                      border: 1px solid #ddd;
                      border-radius: 4px;
                      font-size: 14px;
                    " required>
                  </div>

                  <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Content:</label>
                    <textarea id="campaignContent" rows="10" style="
                      width: 100%;
                      padding: 8px;
                      border: 1px solid #ddd;
                      border-radius: 4px;
                      font-size: 14px;
                      resize: vertical;
                    " required>${campaign.content}</textarea>
                  </div>

                  <div style="text-align: center;">
                    <button type="button" onclick="closeEditModal()" style="
                      background: #6c757d;
                      color: white;
                      border: none;
                      padding: 10px 20px;
                      border-radius: 5px;
                      cursor: pointer;
                      margin-right: 10px;
                    ">Cancel</button>
                    <button type="submit" style="
                      background: #1e3a8a;
                      color: white;
                      border: none;
                      padding: 10px 20px;
                      border-radius: 5px;
                      cursor: pointer;
                    ">Save Changes</button>
                  </div>
                </form>
              </div>
            </div>
          `;

          document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeEditModal() {
          const modal = document.getElementById('editModal');
          if (modal) {
            modal.remove();
          }
        }

        function saveCampaign(event, campaignId) {
          event.preventDefault();

          const name = document.getElementById('campaignName').value;
          const subject = document.getElementById('campaignSubject').value;
          const content = document.getElementById('campaignContent').value;

          // Here you would save the campaign data to your database
          console.log('Saving campaign:', { campaignId, name, subject, content });

          // Show success message
          alert('Campaign saved successfully!');

          // Close modal
          closeEditModal();

          // Refresh the campaign list if needed
          // refreshCampaignList();
        }

        // ===== NAVIGATION & LOGOUT FIXES =====

        // Logout Function
        function logout() {
          // Clear any stored session data
          sessionStorage.clear();
          localStorage.clear();

          // Show logout message
          alert('You have been logged out successfully.');

          // Redirect to homepage
          window.location.href = '/';
        }

        // Make logout function globally available
        window.logout = logout;

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .error {
                border-color: var(--error-color) !important;
            }
            .error-message {
                color: var(--error-color);
                font-size: 0.875rem;
                margin-top: 0.25rem;
                display: block;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
