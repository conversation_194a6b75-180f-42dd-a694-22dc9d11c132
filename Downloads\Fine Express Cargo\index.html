<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Fine Express Cargo - Professional import and export company operating in Tanzania, Dubai, and China. Reliable international logistics and freight operations.">
    <meta name="keywords" content="shipping, import, export, Tanzania, Dubai, China, logistics, freight, cargo, Fine Express Cargo">
    <meta name="author" content="Fine Express Cargo">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Fine Express Cargo - Import & Export Logistics">
    <meta property="og:description" content="Professional import and export services connecting Tanzania, Dubai, and China">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">

    <title>Fine Express Cargo - Import & Export Logistics</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/styles.css">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="assets/js/main.js" as="script">

    <!-- VANTA.js Dependencies -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vanta/0.5.24/vanta.waves.min.js"></script>
</head>
<body>
    <!-- VANTA.WAVES Background Container -->
    <div id="vanta-background" class="vanta-background"></div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p data-translate="loading">Loading...</p>
    </div>

    <!-- Sticky Header -->
    <header id="main-header" class="header sticky-header">
        <div class="container">
            <div class="header-content">
                <!-- Logo -->
                <div class="logo">
                    <img src="assets/images/logo.png" alt="Fine Express Cargo" class="logo-img">
                    <span class="logo-text">Fine Express Cargo</span>
                </div>

                <!-- Navigation -->
                <nav class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li><a href="#home" class="nav-link active" data-translate="nav-home">Home</a></li>
                        <li><a href="about.html" class="nav-link" data-translate="nav-about">About</a></li>
                        <li><a href="#services" class="nav-link" data-translate="nav-services">Services</a></li>
                        <li><a href="#schedule" class="nav-link" data-translate="nav-schedule">Schedule</a></li>
                        <li><a href="gallery.html" class="nav-link" data-translate="nav-gallery">Gallery</a></li>
                        <li><a href="#contact" class="nav-link" data-translate="nav-contact">Contact</a></li>
                    </ul>
                </nav>

                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Quote Button -->
                    <button class="btn btn-primary quote-btn" id="quote-btn" data-translate="get-quote">Get Quote</button>

                    <!-- Admin Login Icon -->
                    <div class="admin-login-section">
                        <a href="login.html" class="admin-login-btn" title="Admin Login" aria-label="Admin Login">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            <span class="admin-login-text">Admin</span>
                        </a>
                    </div>

                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button class="lang-btn" id="lang-btn">
                            <img src="assets/images/flags/en.png" alt="English" class="flag-icon" id="current-flag">
                            <span id="current-lang">EN</span>
                        </button>
                        <div class="lang-dropdown" id="lang-dropdown">
                            <button class="lang-option" data-lang="en">
                                <img src="assets/images/flags/en.png" alt="English" class="flag-icon">
                                <span>English</span>
                            </button>
                            <button class="lang-option" data-lang="sw">
                                <img src="assets/images/flags/tz.png" alt="Swahili" class="flag-icon">
                                <span>Kiswahili</span>
                            </button>
                            <button class="lang-option" data-lang="ar">
                                <img src="assets/images/flags/ar.png" alt="Arabic" class="flag-icon">
                                <span>العربية</span>
                            </button>
                        </div>
                    </div>

                    <!-- Theme Switcher -->
                    <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                        <span class="theme-icon sun-icon">☀️</span>
                        <span class="theme-icon moon-icon">🌙</span>
                    </button>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle menu">
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                        <span class="hamburger-line"></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Quote Modal -->
    <div id="quote-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 data-translate="quote-title">Request a Quote</h2>
                <button class="modal-close" id="quote-modal-close">&times;</button>
            </div>
            <form id="quote-form" class="quote-form">
                <div class="form-group">
                    <label for="quote-name" data-translate="form-name">Full Name *</label>
                    <input type="text" id="quote-name" name="name" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label for="quote-email" data-translate="form-email">Email Address *</label>
                    <input type="email" id="quote-email" name="email" required>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label for="quote-phone" data-translate="form-phone">Phone Number</label>
                    <input type="tel" id="quote-phone" name="phone">
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label for="quote-service" data-translate="form-service">Service Type *</label>
                    <select id="quote-service" name="service" required>
                        <option value="" data-translate="form-select-service">Select Service</option>
                        <option value="import" data-translate="service-import">Import Services</option>
                        <option value="export" data-translate="service-export">Export Services</option>
                        <option value="comoro" data-translate="service-comoro">Comoro Shipment</option>
                        <option value="transhipment" data-translate="service-transhipment">Transhipment</option>
                        <option value="customs" data-translate="service-customs">Custom Clearance</option>
                        <option value="loading" data-translate="service-loading">Loading Permission</option>
                        <option value="transit" data-translate="service-transit">Transit</option>
                    </select>
                    <span class="error-message"></span>
                </div>
                <div class="form-group">
                    <label for="quote-message" data-translate="form-message">Message *</label>
                    <textarea id="quote-message" name="message" rows="4" required></textarea>
                    <span class="error-message"></span>
                </div>
                <button type="submit" class="btn btn-primary" data-translate="form-submit">Send Quote Request</button>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="hero-section" class="hero">
            <div class="hero-slideshow">
                <div class="hero-slide active" data-bg="assets/images/gallery/port-operations-1.jpg"></div>
                <div class="hero-slide" data-bg="assets/images/gallery/port-operations-2.jpg"></div>
                <div class="hero-slide" data-bg="assets/images/gallery/vessel-mv-kaize.jpg"></div>
                <div class="hero-slide" data-bg="assets/images/gallery/warehouse-facility-1.jpg"></div>
                <div class="hero-overlay"></div>
            </div>
            <div class="slideshow-indicators">
                <button class="indicator active" data-slide="0" aria-label="Slide 1"></button>
                <button class="indicator" data-slide="1" aria-label="Slide 2"></button>
                <button class="indicator" data-slide="2" aria-label="Slide 3"></button>
                <button class="indicator" data-slide="3" aria-label="Slide 4"></button>
            </div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title" data-translate="hero-title">Connecting Tanzania, Dubai & China Through Reliable Cargo Solutions</h1>
                    <p class="hero-subtitle" data-translate="hero-subtitle">Professional import and export services with scheduled collections and guaranteed delivery. Your global trade partner for seamless logistics across three continents.</p>
                    <div class="hero-actions">
                        <button class="btn btn-primary btn-large" id="hero-quote-btn" data-translate="get-quote">Get Quote</button>
                        <a href="#services" class="btn btn-secondary btn-large" data-translate="learn-more">Learn More</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Company Introduction -->
        <section class="company-intro">
            <div class="container">
                <div class="intro-content">
                    <div class="intro-text">
                        <h2 data-translate="intro-title">Your Global Import-Export Connection</h2>
                        <p data-translate="intro-description">Fine Express Cargo is a leading import and export shipping company connecting Tanzania, Dubai, and China, providing reliable international logistics solutions. With our extensive network and experienced team, we ensure your cargo reaches its destination safely and on time.</p>
                        <div class="intro-stats">
                            <div class="stat-item">
                                <span class="stat-number">10+</span>
                                <span class="stat-label" data-translate="stat-partners">Partner Vessels</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">100%</span>
                                <span class="stat-label" data-translate="stat-reliability">Reliability</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">24/7</span>
                                <span class="stat-label" data-translate="stat-support">Support</span>
                            </div>
                        </div>
                    </div>
                    <div class="intro-image">
                        <img src="assets/images/company-intro.jpg" alt="Fine Express Cargo Operations" loading="lazy">
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="services">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title" data-translate="services-title">Our Services</h2>
                    <p class="section-subtitle" data-translate="services-subtitle">Comprehensive shipping and logistics solutions for your business needs</p>
                </div>
                <div class="services-grid">
                    <div class="service-card">
                        <div class="service-icon">
                            <img src="assets/images/icons/import.svg" alt="Import Services">
                        </div>
                        <h3 data-translate="service-import-title">Import Services</h3>
                        <p data-translate="service-import-desc">Facilitating imports from China and Dubai to Tanzania with complete documentation support.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <img src="assets/images/icons/export.svg" alt="Export Services">
                        </div>
                        <h3 data-translate="service-export-title">Export Services</h3>
                        <p data-translate="service-export-desc">Managing exports from Tanzania to international markets with efficient coordination.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <img src="assets/images/icons/collection.svg" alt="Cargo Collection">
                        </div>
                        <h3 data-translate="service-collection-title">Cargo Collection</h3>
                        <p data-translate="service-collection-desc">Scheduled pickup services every Wednesday, Thursday, and Friday from Dubai.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <img src="assets/images/icons/delivery.svg" alt="Delivery Services">
                        </div>
                        <h3 data-translate="service-delivery-title">Delivery Services</h3>
                        <p data-translate="service-delivery-desc">Reliable delivery every Tuesday to Tanzania with 4-5 days transit time.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <img src="assets/images/icons/customs.svg" alt="Customs Clearance">
                        </div>
                        <h3 data-translate="service-customs-title">Customs Clearance</h3>
                        <p data-translate="service-customs-desc">Complete documentation and customs handling for smooth cargo processing.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <img src="assets/images/icons/logistics.svg" alt="Logistics Coordination">
                        </div>
                        <h3 data-translate="service-logistics-title">Logistics Coordination</h3>
                        <p data-translate="service-logistics-desc">End-to-end supply chain management and logistics coordination.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Collection & Delivery Schedule Section -->
        <section id="schedule" class="schedule">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title" data-translate="schedule-title">Weekly Collection & Delivery Schedule</h2>
                    <p class="section-subtitle" data-translate="schedule-subtitle">Join our service network for regular updates and priority booking</p>
                </div>

                <div class="schedule-grid">
                    <div class="schedule-card">
                        <div class="schedule-icon">
                            <img src="assets/images/icons/collection.svg" alt="Collection Days">
                        </div>
                        <h3 data-translate="schedule-collection-title">Collection Days</h3>
                        <p class="schedule-days" data-translate="schedule-collection-days">Wednesday, Thursday, Friday</p>
                        <p class="schedule-location">Dubai Collection Points</p>
                    </div>

                    <div class="schedule-card">
                        <div class="schedule-icon">
                            <img src="assets/images/icons/delivery.svg" alt="Delivery Day">
                        </div>
                        <h3 data-translate="schedule-delivery-title">Delivery Day</h3>
                        <p class="schedule-days" data-translate="schedule-delivery-day">Tuesday</p>
                        <p class="schedule-location">Tanzania Destinations</p>
                    </div>

                    <div class="schedule-card">
                        <div class="schedule-icon">
                            <img src="assets/images/icons/transit.svg" alt="Transit Time">
                        </div>
                        <h3 data-translate="schedule-transit-title">Transit Time</h3>
                        <p class="schedule-days" data-translate="schedule-transit-time">4-5 Days</p>
                        <p class="schedule-location">Dubai to Tanzania</p>
                    </div>
                </div>

                <!-- Customer Signup Form -->
                <div class="signup-section">
                    <div class="signup-content">
                        <div class="signup-info">
                            <h3 data-translate="schedule-signup-title">Join Our Service Network</h3>
                            <p data-translate="schedule-signup-desc">Get added to WhatsApp updates group and receive email notifications</p>
                            <ul class="signup-benefits">
                                <li>✓ WhatsApp group updates</li>
                                <li>✓ Email notifications</li>
                                <li>✓ Priority booking</li>
                                <li>✓ Service updates</li>
                            </ul>
                        </div>

                        <div class="signup-form-container">
                            <form id="signup-form" class="signup-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="text" id="signup-name" name="name" placeholder="Full Name *" data-translate="form-name" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="tel" id="signup-phone" name="phone" placeholder="Phone Number *" data-translate="form-phone" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <input type="email" id="signup-email" name="email" placeholder="Email Address *" data-translate="form-email" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" id="signup-location" name="location" placeholder="Location/City" data-translate="form-location">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <input type="text" id="signup-goods" name="goods_type" placeholder="Type of goods (optional)" data-translate="form-goods-type">
                                </div>
                                <button type="submit" class="btn btn-primary btn-large" data-translate="form-signup">Join Our Service Network</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="cta">
            <div class="container">
                <div class="cta-content">
                    <h2 data-translate="cta-title">Ready to Ship with Confidence?</h2>
                    <p data-translate="cta-description">Get in touch with our experienced team for reliable import and export shipping solutions.</p>
                    <div class="cta-actions">
                        <button class="btn btn-primary btn-large" id="cta-quote-btn" data-translate="get-quote">Get Quote</button>
                        <a href="#contact" class="btn btn-secondary btn-large" data-translate="contact-us">Contact Us</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title" data-translate="contact-title">Contact Us</h2>
                    <p class="section-subtitle" data-translate="contact-subtitle">Get in touch for professional shipping services</p>
                </div>
                <div class="contact-content">
                    <div class="contact-info">
                        <!-- Tanzania Office -->
                        <div class="office-section">
                            <h3 data-translate="contact-tanzania-office">Tanzania Office</h3>
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <img src="assets/images/icons/location.svg" alt="Location">
                                </div>
                                <div class="contact-details">
                                    <h4 data-translate="contact-address-title">Address</h4>
                                    <p data-translate="contact-tanzania-address">Lindi na Msimbazi no 82, Near Mskiti wa Lindi</p>
                                </div>
                            </div>
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <img src="assets/images/icons/phone.svg" alt="Phone">
                                </div>
                                <div class="contact-details">
                                    <h4 data-translate="contact-phone-title">Phone</h4>
                                    <p><a href="tel:+255657769101">+*********** 101</a> <span class="phone-label" data-translate="whatsapp-call">(WhatsApp & Call)</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- Dubai Office -->
                        <div class="office-section">
                            <h3 data-translate="contact-dubai-office">Dubai Office</h3>
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <img src="assets/images/icons/location.svg" alt="Location">
                                </div>
                                <div class="contact-details">
                                    <h4 data-translate="contact-address-title">Address</h4>
                                    <p data-translate="contact-dubai-address">Deira Palace Hotel, 118 Sikkat Al Khail Rd, Deira Al Ras, Dubai</p>
                                </div>
                            </div>
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <img src="assets/images/icons/phone.svg" alt="Phone">
                                </div>
                                <div class="contact-details">
                                    <h4 data-translate="contact-phone-title">Phone</h4>
                                    <p><a href="tel:+971543862737">+*********** 737</a> <span class="phone-label" data-translate="whatsapp-call">(WhatsApp & Call)</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- Email & Social -->
                        <div class="contact-item">
                            <div class="contact-icon">
                                <img src="assets/images/icons/email.svg" alt="Email">
                            </div>
                            <div class="contact-details">
                                <h4 data-translate="contact-email-title">Email</h4>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <img src="assets/images/icons/instagram.svg" alt="Instagram">
                            </div>
                            <div class="contact-details">
                                <h4>Instagram</h4>
                                <p><a href="https://instagram.com/FINE_CARGO" target="_blank">@FINE_CARGO</a></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <img src="assets/images/icons/clock.svg" alt="Business Hours">
                            </div>
                            <div class="contact-details">
                                <h4 data-translate="contact-hours-title">Business Hours</h4>
                                <p data-translate="contact-hours">Mon - Fri: 8:00 AM - 6:00 PM</p>
                                <p data-translate="contact-hours-weekend">Sat: 8:00 AM - 2:00 PM</p>
                            </div>
                        </div>
                    </div>
                    <div class="contact-form-container">
                        <form id="contact-form" class="contact-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-name" data-translate="form-name">Full Name *</label>
                                    <input type="text" id="contact-name" name="name" required>
                                    <span class="error-message"></span>
                                </div>
                                <div class="form-group">
                                    <label for="contact-email" data-translate="form-email">Email Address *</label>
                                    <input type="email" id="contact-email" name="email" required>
                                    <span class="error-message"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="contact-subject" data-translate="form-subject">Subject *</label>
                                <input type="text" id="contact-subject" name="subject" required>
                                <span class="error-message"></span>
                            </div>
                            <div class="form-group">
                                <label for="contact-message" data-translate="form-message">Message *</label>
                                <textarea id="contact-message" name="message" rows="5" required></textarea>
                                <span class="error-message"></span>
                            </div>
                            <button type="submit" class="btn btn-primary" data-translate="form-send">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/images/logo.png" alt="Fine Express Cargo" class="footer-logo-img">
                        <span class="footer-logo-text">Fine Express Cargo</span>
                    </div>
                    <p data-translate="footer-description">Professional import and export services connecting Tanzania, Dubai, and China with reliable logistics solutions.</p>
                </div>
                <div class="footer-section">
                    <h3 data-translate="footer-services-title">Services</h3>
                    <ul class="footer-links">
                        <li><a href="#services" data-translate="service-import">Import Services</a></li>
                        <li><a href="#services" data-translate="service-export">Export Services</a></li>
                        <li><a href="#services" data-translate="service-collection">Cargo Collection</a></li>
                        <li><a href="#services" data-translate="service-delivery">Delivery Services</a></li>
                        <li><a href="#services" data-translate="service-customs">Customs Clearance</a></li>
                        <li><a href="#services" data-translate="service-logistics">Logistics Coordination</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-translate="footer-company-title">Company</h3>
                    <ul class="footer-links">
                        <li><a href="about.html" data-translate="nav-about">About Us</a></li>
                        <li><a href="gallery.html" data-translate="nav-gallery">Gallery</a></li>
                        <li><a href="#schedule" data-translate="nav-schedule">Schedule</a></li>
                        <li><a href="#contact" data-translate="nav-contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3 data-translate="footer-contact-title">Contact Info</h3>
                    <div class="footer-contact">
                        <p><strong data-translate="contact-tanzania-office">Tanzania:</strong> <a href="tel:+255657769101">+*********** 101</a></p>
                        <p><strong data-translate="contact-dubai-office">Dubai:</strong> <a href="tel:+971543862737">+*********** 737</a></p>
                        <p><strong data-translate="contact-email-title">Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><strong>Instagram:</strong> <a href="https://instagram.com/FINE_CARGO" target="_blank">@FINE_CARGO</a></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; 2024 Fine Express Cargo. <span data-translate="footer-rights">All rights reserved.</span></p>
                    <div class="footer-social">
                        <a href="https://facebook.com/fineexpresscargo" class="social-link" aria-label="Facebook" target="_blank" rel="noopener noreferrer">
                            <img src="assets/images/icons/facebook.svg" alt="Facebook">
                        </a>
                        <a href="https://linkedin.com/company/fine-express-cargo" class="social-link" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                            <img src="assets/images/icons/linkedin.svg" alt="LinkedIn">
                        </a>
                        <a href="https://wa.me/255657769101" class="social-link" aria-label="WhatsApp" target="_blank" rel="noopener noreferrer">
                            <img src="assets/images/icons/whatsapp.svg" alt="WhatsApp">
                        </a>
                        <a href="https://instagram.com/FINE_CARGO" class="social-link" aria-label="Instagram" target="_blank" rel="noopener noreferrer">
                            <img src="assets/images/icons/instagram.svg" alt="Instagram">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="Back to top">
        <img src="assets/images/icons/arrow-up.svg" alt="Back to top">
    </button>

    <!-- Success/Error Messages -->
    <div id="message-container" class="message-container"></div>

    <!-- Scripts -->
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- VANTA.WAVES Animation -->
    <script>
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, checking for VANTA...');

  // Check if required libraries are loaded
  if (typeof THREE === 'undefined') {
    console.error('Three.js not loaded');
    return;
  }

  if (typeof VANTA === 'undefined') {
    console.error('VANTA library not loaded');
    return;
  }

  // Check if target element exists
  const heroElement = document.getElementById('hero-section');
  if (!heroElement) {
    console.error('Hero section element not found');
    return;
  }

  console.log('Initializing VANTA animation...');

  try {
    const vantaEffect = VANTA.WAVES({
      el: "#hero-section",
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      shininess: 91.00,
      waveHeight: 37.00,
      waveSpeed: 0.90,
      zoom: 0.70,
      color: 0x1e3a8a,        // Navy blue
      backgroundColor: 0x0f172a, // Dark navy background
      waveColor: 0xfb923c      // Orange waves
    });

    console.log('VANTA animation initialized successfully');

    // Destroy animation on page unload to prevent memory leaks
    window.addEventListener('beforeunload', function() {
      if (vantaEffect) vantaEffect.destroy();
    });

  } catch (error) {
    console.error('Error initializing VANTA animation:', error);
  }
});
    </script>
</body>
</html>
